import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  BackHandler,
  Text,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import PhotoCapture from '../../../components/Larges/PhotoCapture';
import useUploadMedia from '../../../hooks/useUploadMedia';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import VideoRecordingComp from '../../../components/Larges/VideoRecordingComp';
import { apiService } from '../../../services/api';

const CapturePackageBoxDispatcher = () => {
  const { selectedEvidences, originalBoxId } = useLocalSearchParams();
  const router = useRouter();
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const { uploadMedia, error } = useUploadMedia();
  const { token } = useAuth();
  const [videoVisible, setVideoVisible] = useState(false);

  // Parse selected evidences from params
  const evidencesData = selectedEvidences ? JSON.parse(selectedEvidences) : {};
  const evidenceIds = Object.values(evidencesData).map(ev => ev.evidenceId);

  useEffect(() => {
    const backAction = () => {
      if (cameraVisible) {
        setCameraVisible(false);
        return true;
      }
      if (videoVisible) {
        setVideoVisible(false);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [cameraVisible, videoVisible]);

  const handleCaptureOption = (option) => {
    setShowOptions(false);
    if (option === 'photo') {
      setCameraVisible(true);
    } else if (option === 'video') {
      setVideoVisible(true);
    }
  };

  const handleDeleteMedia = (index) => {
    setMedia((prevMedia) => prevMedia.filter((_, i) => i !== index));
  };

  const handlePhotoCaptured = (uriOrUris) => {
    if (Array.isArray(uriOrUris)) {
      // Batch mode - multiple photos
      const newMedia = uriOrUris.map(uri => ({
        id: `${Date.now()}_${Math.random()}`,
        uri,
        type: 'image',
        uploaded: false
      }));
      setMedia((prevMedia) => [...prevMedia, ...newMedia]);
    } else {
      // Single mode - one photo
      const mediaId = Date.now().toString();
      setMedia((prevMedia) => [
        ...prevMedia,
        { id: mediaId, uri: uriOrUris, type: 'image', uploaded: false }
      ]);
    }
    setCameraVisible(false);
  };

  const handleVideoCaptured = (uri) => {
    const mediaId = Date.now().toString();
    setMedia((prevMedia) => [
      ...prevMedia,
      { id: mediaId, uri, type: 'video', uploaded: false }
    ]);
    setVideoVisible(false);
  };

  const completeDispatchPackaging = async () => {
    if (media.length === 0) {
      Alert.alert('Error', 'Please capture at least one photo of the new package');
      return;
    }

    setIsSaving(true);
    setIsUploading(true);

    try {
      console.log('🚚 Starting dispatcher packaging process...');
      console.log('🚚 Original Box ID:', originalBoxId);
      console.log('🚚 Selected Evidences:', evidencesData);
      console.log('🚚 Evidence IDs:', evidenceIds);

      // Step 1: Upload media files
      const uploadedUrls = [];
      for (let i = 0; i < media.length; i++) {
        const mediaItem = media[i];
        setUploadProgress(((i + 1) / media.length) * 50); // First 50% for uploads
        
        console.log(`🚚 Uploading media ${i + 1}/${media.length}:`, mediaItem.uri);
        const uploadedUrl = await uploadMedia(mediaItem.uri, mediaItem.type);
        
        if (uploadedUrl) {
          uploadedUrls.push(uploadedUrl);
          console.log(`🚚 Media ${i + 1} uploaded:`, uploadedUrl);
        } else {
          throw new Error(`Failed to upload media ${i + 1}`);
        }
      }

      console.log('🚚 All media uploaded:', uploadedUrls);
      setUploadProgress(60);

      // Step 2: Mark original box as accepted
      console.log('🚚 Marking original box as accepted...');
      const updateResult = await apiService.updateEvidenceBoxPackaging(
        token,
        originalBoxId,
        'accepted',
        'Partial dispatch - new package created'
      );

      if (!updateResult || updateResult.status !== 'success') {
        throw new Error('Failed to update original box status');
      }
      console.log('🚚 Original box marked as accepted');
      setUploadProgress(70);

      // Step 3: Create new package box with selected evidences
      console.log('🚚 Creating new package box...');
      const packagingResult = await apiService.addEvidenceBoxPackaging(
        token,
        uploadedUrls, // Array of packaging URLs
        evidenceIds, // Array of selected evidence IDs
        'dispatch'
      );

      console.log('🚚 New package box result:', JSON.stringify(packagingResult, null, 2));
      setUploadProgress(90);

      if (!packagingResult || packagingResult.status !== 'success') {
        throw new Error('Failed to create new evidence box packaging');
      }

      setUploadProgress(100);
      setIsSuccessVisible(true);

      // Return to previous screen with success
      setTimeout(() => {
        router.back();
        // You might want to pass success data back to the previous screen
      }, 2000);

    } catch (error) {
      console.error('🚚 Error in dispatcher packaging:', error);
      Alert.alert('Error', error.message || 'Failed to complete packaging process');
    } finally {
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  if (isSuccessVisible) {
    return (
      <SuccessScreen
        title="Package Created Successfully!"
        subtitle="New evidence package has been created and is ready for dispatch"
        onClose={() => setIsSuccessVisible(false)}
      />
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <CapturePageHeader 
        title="Package New Evidence Box"
        subtitle={`Creating new package for ${evidenceIds.length} evidence${evidenceIds.length !== 1 ? 's' : ''}`}
        onBack={() => router.back()}
      />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.instructionContainer}>
          <Text style={styles.instructionTitle}>📦 Package Documentation Required</Text>
          <Text style={styles.instructionText}>
            Since you're creating a new package with selected evidences, please capture photos of:
          </Text>
          <Text style={styles.instructionBullet}>• Package exterior (sealed)</Text>
          <Text style={styles.instructionBullet}>• Package interior (before sealing)</Text>
          <Text style={styles.instructionBullet}>• Evidence arrangement</Text>
        </View>

        <MediaGrid 
          media={media}
          onDeleteMedia={handleDeleteMedia}
        />

        <CaptureButton onPress={() => setShowOptions(true)} />

        <TouchableOpacity 
          style={[
            styles.completeButton,
            (media.length === 0 || isSaving) && styles.completeButtonDisabled
          ]}
          onPress={completeDispatchPackaging}
          disabled={media.length === 0 || isSaving}
        >
          {isSaving ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="white" size="small" />
              <Text style={styles.completeButtonText}>
                {isUploading ? `Uploading... ${uploadProgress}%` : 'Creating Package...'}
              </Text>
            </View>
          ) : (
            <Text style={styles.completeButtonText}>
              Complete Package Creation
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      <OptionsOverlay
        visible={showOptions}
        onClose={() => setShowOptions(false)}
        onSelectOption={handleCaptureOption}
      />

      {cameraVisible && (
        <View style={styles.cameraOverlay}>
          <PhotoCapture
            setUri={handlePhotoCaptured}
            backPressed={() => setCameraVisible(false)}
          />
        </View>
      )}

      {videoVisible && (
        <View style={styles.cameraOverlay}>
          <VideoRecordingComp
            setUri={handleVideoCaptured}
            backPressed={() => setVideoVisible(false)}
            onRecordingComplete={() => setVideoVisible(false)}
          />
        </View>
      )}
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
  },
  errorContainer: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#ffecec',
    borderRadius: 5,
    marginTop: 10,
  },
  errorText: {
    color: '#ff0000',
  },
  uploadSummary: {
    padding: 10,
    marginHorizontal: 10,
    backgroundColor: '#f0f8ff',
    borderRadius: 5,
    marginTop: 10,
  },
  uploadTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  uploadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  uploadingText: {
    marginLeft: 10,
    color: '#0066cc',
  },
  submitButton: {
    marginTop: 10,
    backgroundColor: '#0B36A1',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  submitbuttonContainer: {
    paddingHorizontal: 20,
  },
  progressBarContainer: {
    height: 10,
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginVertical: 5,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
});

export default CapturePackageBoxDispatcher;
