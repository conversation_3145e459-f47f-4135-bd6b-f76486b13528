import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  BackHandler,
  Text,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import CapturePageHeader from '../../../components/Smalls/CapturePageHeader';
import CaptureButton from '../../../components/Smalls/CaptureButton';
import OptionsOverlay from '../../../components/Smalls/OptionsOverlay';
import MediaGrid from '../../../components/Smalls/MediaGrid';
import PhotoCapture from '../../../components/Larges/PhotoCapture';
import useUploadMedia from '../../../hooks/useUploadMedia';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import VideoRecordingComp from '../../../components/Larges/VideoRecordingComp';
import { apiService } from '../../../services/api';

const CapturePackageBoxDispatcher = () => {
  const { selectedEvidences, originalBoxId } = useLocalSearchParams();
  const router = useRouter();
  const [showOptions, setShowOptions] = useState(false);
  const [media, setMedia] = useState([]);
  const [cameraVisible, setCameraVisible] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const { uploadMedia, error } = useUploadMedia();
  const { token } = useAuth();
  const [videoVisible, setVideoVisible] = useState(false);

  // Parse selected evidences from params
  const evidencesData = selectedEvidences ? JSON.parse(selectedEvidences) : {};
  const evidenceIds = Object.values(evidencesData).map(ev => ev.evidenceId);

  useEffect(() => {
    const backAction = () => {
      if (cameraVisible) {
        setCameraVisible(false);
        return true;
      }
      if (videoVisible) {
        setVideoVisible(false);
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [cameraVisible, videoVisible]);

  const handleCapturePhoto = () => {
    setShowOptions(false);
    setCameraVisible(true);
  };

  const handleCaptureVideo = () => {
    setShowOptions(false);
    setVideoVisible(true);
  };

  const handleMediaCaptured = (mediaUri, mediaType) => {
    const newMedia = {
      uri: mediaUri,
      type: mediaType,
      id: Date.now().toString(),
    };
    setMedia(prev => [...prev, newMedia]);
    setCameraVisible(false);
    setVideoVisible(false);
  };

  const handleDeleteMedia = (mediaId) => {
    setMedia(prev => prev.filter(item => item.id !== mediaId));
  };

  const completeDispatchPackaging = async () => {
    if (media.length === 0) {
      Alert.alert('Error', 'Please capture at least one photo of the new package');
      return;
    }

    setIsSaving(true);
    setIsUploading(true);

    try {
      console.log('🚚 Starting dispatcher packaging process...');
      console.log('🚚 Original Box ID:', originalBoxId);
      console.log('🚚 Selected Evidences:', evidencesData);
      console.log('🚚 Evidence IDs:', evidenceIds);

      // Step 1: Upload media files
      const uploadedUrls = [];
      for (let i = 0; i < media.length; i++) {
        const mediaItem = media[i];
        setUploadProgress(((i + 1) / media.length) * 50); // First 50% for uploads
        
        console.log(`🚚 Uploading media ${i + 1}/${media.length}:`, mediaItem.uri);
        const uploadedUrl = await uploadMedia(mediaItem.uri, mediaItem.type);
        
        if (uploadedUrl) {
          uploadedUrls.push(uploadedUrl);
          console.log(`🚚 Media ${i + 1} uploaded:`, uploadedUrl);
        } else {
          throw new Error(`Failed to upload media ${i + 1}`);
        }
      }

      console.log('🚚 All media uploaded:', uploadedUrls);
      setUploadProgress(60);

      // Step 2: Mark original box as accepted
      console.log('🚚 Marking original box as accepted...');
      const updateResult = await apiService.updateEvidenceBoxPackaging(
        token,
        originalBoxId,
        'accepted',
        'Partial dispatch - new package created'
      );

      if (!updateResult || updateResult.status !== 'success') {
        throw new Error('Failed to update original box status');
      }
      console.log('🚚 Original box marked as accepted');
      setUploadProgress(70);

      // Step 3: Create new package box with selected evidences
      console.log('🚚 Creating new package box...');
      const packagingResult = await apiService.addEvidenceBoxPackaging(
        token,
        uploadedUrls, // Array of packaging URLs
        evidenceIds, // Array of selected evidence IDs
        'dispatch'
      );

      console.log('🚚 New package box result:', JSON.stringify(packagingResult, null, 2));
      setUploadProgress(90);

      if (!packagingResult || packagingResult.status !== 'success') {
        throw new Error('Failed to create new evidence box packaging');
      }

      setUploadProgress(100);
      setIsSuccessVisible(true);

      // Return to previous screen with success
      setTimeout(() => {
        router.back();
        // You might want to pass success data back to the previous screen
      }, 2000);

    } catch (error) {
      console.error('🚚 Error in dispatcher packaging:', error);
      Alert.alert('Error', error.message || 'Failed to complete packaging process');
    } finally {
      setIsSaving(false);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  if (isSuccessVisible) {
    return (
      <SuccessScreen
        title="Package Created Successfully!"
        subtitle="New evidence package has been created and is ready for dispatch"
        onClose={() => setIsSuccessVisible(false)}
      />
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <CapturePageHeader 
        title="Package New Evidence Box"
        subtitle={`Creating new package for ${evidenceIds.length} evidence${evidenceIds.length !== 1 ? 's' : ''}`}
        onBack={() => router.back()}
      />

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.instructionContainer}>
          <Text style={styles.instructionTitle}>📦 Package Documentation Required</Text>
          <Text style={styles.instructionText}>
            Since you're creating a new package with selected evidences, please capture photos of:
          </Text>
          <Text style={styles.instructionBullet}>• Package exterior (sealed)</Text>
          <Text style={styles.instructionBullet}>• Package interior (before sealing)</Text>
          <Text style={styles.instructionBullet}>• Evidence arrangement</Text>
        </View>

        <MediaGrid 
          media={media}
          onDeleteMedia={handleDeleteMedia}
        />

        <CaptureButton onPress={() => setShowOptions(true)} />

        <TouchableOpacity 
          style={[
            styles.completeButton,
            (media.length === 0 || isSaving) && styles.completeButtonDisabled
          ]}
          onPress={completeDispatchPackaging}
          disabled={media.length === 0 || isSaving}
        >
          {isSaving ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color="white" size="small" />
              <Text style={styles.completeButtonText}>
                {isUploading ? `Uploading... ${uploadProgress}%` : 'Creating Package...'}
              </Text>
            </View>
          ) : (
            <Text style={styles.completeButtonText}>
              Complete Package Creation
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>

      <OptionsOverlay
        visible={showOptions}
        onClose={() => setShowOptions(false)}
        onCapturePhoto={handleCapturePhoto}
        onCaptureVideo={handleCaptureVideo}
      />

      {cameraVisible && (
        <PhotoCapture
          onCapture={(uri) => handleMediaCaptured(uri, 'image')}
          onClose={() => setCameraVisible(false)}
        />
      )}

      {videoVisible && (
        <VideoRecordingComp
          onCapture={(uri) => handleMediaCaptured(uri, 'video')}
          onClose={() => setVideoVisible(false)}
        />
      )}
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  instructionContainer: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  instructionBullet: {
    fontSize: 13,
    color: '#555',
    marginLeft: 8,
    marginBottom: 4,
  },
  completeButton: {
    backgroundColor: '#0a34a1',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  completeButtonDisabled: {
    backgroundColor: '#ccc',
  },
  completeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default CapturePackageBoxDispatcher;
