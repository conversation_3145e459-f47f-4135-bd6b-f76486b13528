import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Image,
  Alert,
  StyleSheet,
  ActivityIndicator,
  FlatList,
  Modal,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { transformUrl } from '../../../utils/transformUrl';
import { useAuth } from '../../../context/auth-context';
import SuccessScreen from '../../../components/Smalls/SuccessScreen';
import { MaterialIcons } from '@expo/vector-icons';
import PreviewComponent from '../../../components/Larges/PreviewComponent';
import { Colors } from '../../../constants/colors';
import { apiService } from '../../../services/api';



const ForensicRequestDetails = () => {
  const params = useLocalSearchParams();


  let forensicRequestId;
  let boxId;
  let caseIds;



  if (params.forensicRequestId) {
    forensicRequestId = params.forensicRequestId;
  } else if (params.caseData) {
    try {
      const caseData = JSON.parse(params.caseData);
      forensicRequestId = caseData.forensicRequestId;
    } catch (e) {
      console.error('Error parsing case data:', e);
    }
  }

  // Extract boxId and caseIds from packageData if available
  if (params.packageData) {
    try {
      const packageData = JSON.parse(params.packageData);
      boxId = packageData.boxId;
      caseIds = packageData.caseIds; // Array of case IDs
      console.log('Extracted boxId from packageData:', boxId);
      console.log('Extracted caseIds from packageData:', caseIds);
    } catch (e) {
      console.error('Error parsing package data:', e);
    }
  }
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [segregatedEvidences, setSegregatedEvidences] = useState(null);
  const [selectedEvidences, setSelectedEvidences] = useState({});
  const [allEvidencesSelected, setAllEvidencesSelected] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { token } = useAuth();
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [isVideo, setIsVideo] = useState(false);
  const [extractedForensicRequestIds, setExtractedForensicRequestIds] = useState([]);
  const [allForensicData, setAllForensicData] = useState([]);

  // Fetch data from API
  useEffect(() => {
    const fetchData = async () => {
      // If we have boxId but no forensicRequestId, skip this effect
      // The forensicRequestId will be extracted from the Evidence Box API
      if (boxId && !forensicRequestId) {
        return;
      }

      if (!forensicRequestId) {
        setError('Forensic request ID is missing');
        setLoading(false);
        return;
      }

      try {
        const result = await apiService.fetchForensicRequestEvidences(token, forensicRequestId);

        // Log response without QR codes
        const cleanResult = JSON.parse(JSON.stringify(result));
        if (cleanResult.data?.qrCode) {
          delete cleanResult.data.qrCode;
        }
        console.log('Forensic Request Evidences (without QR):', JSON.stringify(cleanResult, null, 2));

        if (result.status !== 'success') {
          throw new Error(result.message || 'Failed to fetch evidence data');
        }

        setData(result.data);

        // Process and segregate evidences by lab and department
        if (result.data) {
          const segregated = segregateEvidences(result.data);
          setSegregatedEvidences(segregated);
        }
      } catch (error) {
        console.error('Error fetching evidence data:', error);
        setError(error.message || 'Failed to fetch evidence data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [forensicRequestId, token, boxId]);

  // Fetch evidence box data when boxId is available
  useEffect(() => {
    const fetchEvidenceBoxData = async () => {
      if (!boxId || !token) {
        return;
      }

      try {
        console.log('Calling getEvidencesOfABox API with boxId:', boxId);
        const result = await apiService.getEvidencesOfABox(token, boxId);

        // Log response without QR codes
        const cleanResult = JSON.parse(JSON.stringify(result));
        if (cleanResult.data?.evidencePackaging) {
          // Remove QR code from main packaging
          delete cleanResult.data.evidencePackaging.qrCode;

          // Remove QR codes from evidence forensic requests
          if (cleanResult.data.evidencePackaging.evidenceId) {
            cleanResult.data.evidencePackaging.evidenceId.forEach(evidence => {
              if (evidence.forensicRequests) {
                evidence.forensicRequests.forEach(request => {
                  delete request.qrCode;
                });
              }
            });
          }
        }
        console.log('Evidence Box API Response (without QR codes):', JSON.stringify(cleanResult, null, 2));

        // Extract all forensicRequestIds from the response and call fetchForensicRequestEvidences for each
        if (result && result.data && result.data.evidencePackaging) {
          const evidencePackaging = result.data.evidencePackaging;
          const forensicRequestIds = [];
          const forensicDataArray = [];
          const evidenceLabAssignments = {}; // Store lab assignments with priorities

          // Collect all forensic request IDs and lab assignments
          if (evidencePackaging.evidenceId && Array.isArray(evidencePackaging.evidenceId)) {
            for (const evidence of evidencePackaging.evidenceId) {
              // Store lab assignments with priorities for this evidence
              if (evidence.labAssignments && Array.isArray(evidence.labAssignments)) {
                evidenceLabAssignments[evidence._id] = evidence.labAssignments;
                console.log('Lab assignments for evidence', evidence._id, ':', evidence.labAssignments);
              }

              if (evidence.forensicRequests && Array.isArray(evidence.forensicRequests)) {
                for (const forensicRequest of evidence.forensicRequests) {
                  if (forensicRequest._id) {
                    forensicRequestIds.push({
                      id: forensicRequest._id,
                      evidenceId: evidence._id,
                      labAssignments: evidence.labAssignments || []
                    });
                  }
                }
              }
            }
          }

          console.log('=== EVIDENCE BOX SUMMARY ===');
          console.log('Box ID:', boxId);
          console.log('Total Evidences Found:', evidencePackaging.evidenceId?.length || 0);
          console.log('Total Forensic Requests:', forensicRequestIds.length);

          // Log each evidence with its lab assignments
          evidencePackaging.evidenceId?.forEach((evidence, index) => {
            console.log(`\nEvidence ${index + 1}:`);
            console.log('  - ID:', evidence._id);
            console.log('  - Title:', evidence.title);
            console.log('  - Lab Assignments:', evidence.labAssignments?.length || 0);
            evidence.labAssignments?.forEach((assignment, i) => {
              console.log(`    ${i + 1}. ${assignment.labDepartmentId?.name} (Priority: ${assignment.priority})`);
            });
            console.log('  - Forensic Requests:', evidence.forensicRequests?.length || 0);
          });

          setExtractedForensicRequestIds(forensicRequestIds.map(item => item.id));

          // Fetch evidence data for each forensic request ID
          for (const forensicRequestData of forensicRequestIds) {
            try {
              console.log('Calling fetchForensicRequestEvidences API with forensicRequestId:', forensicRequestData.id);
              const forensicResult = await apiService.fetchForensicRequestEvidences(token, forensicRequestData.id);

              // Log response without QR codes
              const cleanForensicResult = JSON.parse(JSON.stringify(forensicResult));
              if (cleanForensicResult.data?.qrCode) {
                delete cleanForensicResult.data.qrCode;
              }
              console.log('Forensic Request Evidences API Response (without QR):', JSON.stringify(cleanForensicResult, null, 2));

              if (forensicResult.status === 'success' && forensicResult.data) {
                // Add lab assignments data to the forensic result
                forensicResult.data.labAssignments = forensicRequestData.labAssignments;
                forensicDataArray.push(forensicResult.data);
              }
            } catch (forensicError) {
              console.error('Error fetching forensic request evidences for forensicRequestId', forensicRequestData.id, ':', forensicError);
            }
          }

          // Store all forensic data
          setAllForensicData(forensicDataArray);
          console.log('All forensic data collected:', forensicDataArray.length, 'items');

          // Process and segregate all evidences by lab and department
          if (forensicDataArray.length > 0) {
            const allSegregated = segregateMultipleEvidences(forensicDataArray);

            console.log('\n=== FINAL SEGREGATION SUMMARY ===');
            let totalEvidenceEntries = 0;
            Object.entries(allSegregated).forEach(([, lab]) => {
              console.log(`\nLab: ${lab.name}`);
              Object.entries(lab.departments).forEach(([, department]) => {
                console.log(`  - ${department.name} (Priority: ${department.priority}): ${department.evidences.length} evidence(s)`);
                department.evidences.forEach(evidence => {
                  console.log(`    * ${evidence.title} (ID: ${evidence._id})`);
                });
                totalEvidenceEntries += department.evidences.length;
              });
            });
            console.log(`\nTotal Evidence Entries in UI: ${totalEvidenceEntries}`);
            console.log('=================================\n');

            setSegregatedEvidences(allSegregated);
            setLoading(false);
          }
        }
      } catch (error) {
        console.error('Error fetching evidence box data:', error);
        setError(error.message || 'Failed to fetch evidence box data');
        setLoading(false);
      }
    };

    fetchEvidenceBoxData();
  }, [boxId, token]);

  // Function to segregate evidences by lab and department
  const segregateEvidences = (data) => {
    const segregated = {};

    // Check if data has the expected structure
    if (!data.evidence) {
      return segregated;
    }

    // Create a single evidence object
    const evidence = data.evidence;
    const labId = data.labId?._id || 'unknown';
    const labName = data.labId?.name || 'Unknown Lab';
    const departmentId = data.labdepartmentId?._id || 'unknown';
    const departmentName = data.labdepartmentId?.name || 'Unknown Department';

    // Initialize lab if it doesn't exist
    if (!segregated[labId]) {
      segregated[labId] = {
        name: labName,
        departments: {}
      };
    }

    // Initialize department if it doesn't exist
    if (!segregated[labId].departments[departmentId]) {
      segregated[labId].departments[departmentId] = {
        name: departmentName,
        evidences: []
      };
    }

    // Add evidence to the department
    segregated[labId].departments[departmentId].evidences.push({
      ...evidence,
      forensicRequestId: data._id
    });

    return segregated;
  };

  // Function to segregate multiple evidences from multiple forensic requests
  const segregateMultipleEvidences = (forensicDataArray) => {
    const segregated = {};

    for (const data of forensicDataArray) {
      // Check if data has the expected structure
      if (!data.evidence) {
        continue;
      }

      // Create evidence object
      const evidence = data.evidence;

      // Process each lab assignment to show evidence in multiple departments
      if (data.labAssignments && Array.isArray(data.labAssignments)) {
        for (const assignment of data.labAssignments) {
          const labId = assignment.labId?._id || 'unknown';
          const labName = assignment.labId?.name || 'Unknown Lab';
          const departmentId = assignment.labDepartmentId?._id || 'unknown';
          const departmentName = assignment.labDepartmentId?.name || 'Unknown Department';
          const priority = assignment.priority;

          // Initialize lab if it doesn't exist
          if (!segregated[labId]) {
            segregated[labId] = {
              name: labName,
              departments: {}
            };
          }

          // Initialize department if it doesn't exist
          if (!segregated[labId].departments[departmentId]) {
            segregated[labId].departments[departmentId] = {
              name: departmentName,
              priority: priority,
              evidences: []
            };
          }

          // Add evidence to the department with unique key to avoid duplicates
          const evidenceKey = `${evidence._id}-${departmentId}`;
          const existingEvidence = segregated[labId].departments[departmentId].evidences.find(
            ev => ev._id === evidence._id
          );

          if (!existingEvidence) {
            segregated[labId].departments[departmentId].evidences.push({
              ...evidence,
              forensicRequestId: data._id,
              priority: priority,
              departmentId: departmentId, // Add department ID for selection tracking
              evidenceKey: evidenceKey
            });
          }
        }
      } else {
        // Fallback to original logic if no lab assignments
        const labId = data.labId?._id || 'unknown';
        const labName = data.labId?.name || 'Unknown Lab';
        const departmentId = data.labdepartmentId?._id || 'unknown';
        const departmentName = data.labdepartmentId?.name || 'Unknown Department';

        if (!segregated[labId]) {
          segregated[labId] = {
            name: labName,
            departments: {}
          };
        }

        if (!segregated[labId].departments[departmentId]) {
          segregated[labId].departments[departmentId] = {
            name: departmentName,
            priority: null,
            evidences: []
          };
        }

        segregated[labId].departments[departmentId].evidences.push({
          ...evidence,
          forensicRequestId: data._id,
          priority: null,
          departmentId: departmentId,
          evidenceKey: `${evidence._id}-${departmentId}`
        });
      }
    }

    return segregated;
  };

  const toggleAllEvidences = () => {
    if (allEvidencesSelected) {
      // Deselect all evidences
      setSelectedEvidences({});
      setAllEvidencesSelected(false);
    } else {
      // Select all evidences
      const allEvidences = {};

      Object.entries(segregatedEvidences).forEach(([labId, lab]) => {
        Object.entries(lab.departments).forEach(([departmentId, department]) => {
          department.evidences.forEach(evidence => {
            const key = `${labId}-${departmentId}-${evidence._id}`;
            allEvidences[key] = {
              labId,
              departmentId,
              evidenceId: evidence._id,
              forensicRequestId: evidence.forensicRequestId,
              priority: evidence.priority,
            };
          });
        });
      });

      setSelectedEvidences(allEvidences);
      setAllEvidencesSelected(true);
    }
  };

  const toggleEvidenceSelection = (labId, departmentId, evidence) => {
    setSelectedEvidences((prev) => {
      const key = `${labId}-${departmentId}-${evidence._id}`;
      let newSelection = { ...prev };

      if (newSelection[key]) {
        // Deselecting this evidence in this department
        delete newSelection[key];
      } else {
        // Selecting this evidence in this department
        newSelection[key] = {
          labId,
          departmentId,
          evidenceId: evidence._id,
          forensicRequestId: evidence.forensicRequestId,
          priority: evidence.priority,
        };
      }

      // Update allEvidencesSelected state based on selection
      const totalEvidences = countTotalEvidences();
      setAllEvidencesSelected(Object.keys(newSelection).length === totalEvidences);

      return newSelection;
    });
  };

  const countTotalEvidences = () => {
    let count = 0;
    if (segregatedEvidences) {
      Object.values(segregatedEvidences).forEach(lab => {
        Object.values(lab.departments).forEach(department => {
          count += department.evidences.length;
        });
      });
    }
    return count;
  };

  const isEvidenceSelected = (labId, departmentId, evidenceId) => {
    const key = `${labId}-${departmentId}-${evidenceId}`;
    return !!selectedEvidences[key];
  };
  
  const isMediaVideo = (url) => {
    if (!url) return false;
    return url.toLowerCase().endsWith('.mp4') || 
           url.toLowerCase().endsWith('.mov') || 
           url.toLowerCase().endsWith('.avi');
  };

  const openMediaModal = (url) => {
    const isVideoFile = isMediaVideo(url);
    setIsVideo(isVideoFile);
    setSelectedMedia(transformUrl(url));
    setModalVisible(true);
  };

  const handleDispatch = async () => {
    // For now, we'll handle dispatch logic later as you mentioned
    // This is a placeholder for when you tell me how to handle multiple forensic request IDs

    if (Object.keys(selectedEvidences).length === 0) {
      Alert.alert('Error', 'Please select at least one evidence to dispatch');
      return;
    }

    setIsSubmitting(true);

    try {
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // TODO: Update this logic based on your requirements for handling multiple forensic requests
      // For now, using the first forensic request ID if available
      const requestIdToUse = extractedForensicRequestIds.length > 0 ? extractedForensicRequestIds[0] : forensicRequestId;

      console.log('Selected evidences for dispatch:', selectedEvidences);
      console.log('Available forensic request IDs:', extractedForensicRequestIds);

      if (!requestIdToUse) {
        Alert.alert('Error', 'Forensic request ID is missing');
        return;
      }

      const result = await apiService.dispatchForensicRequest(token, requestIdToUse);
  
      // Extract important data from the response
      const dispatcherQr = result.data?.qrCode;
      const requestId = result.data?._id;
      const caseId = result.data?.caseId;
      
      // Reset state
      setSelectedEvidences({});
      setAllEvidencesSelected(false);
  
      setShowSuccessScreen(true);
      setTimeout(() => {
        setShowSuccessScreen(false);
        router.replace({
          pathname: '(screensDispatchers)/dipatchQr',
          params: { 
            dispatchData: JSON.stringify({
              dispatcherQr,
              requestId,
              caseId
            }) 
          }
        });
      }, 3000);
      
    } catch (error) {
      console.error('Error dispatching evidence:', error);
      Alert.alert('Error', `Failed to dispatch evidence: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderEvidenceCard = (evidence, labId, departmentId) => {
    const isSelected = isEvidenceSelected(labId, departmentId, evidence._id);
    const hasAttachments = evidence.attachmentUrl && evidence.attachmentUrl.length > 0;
    const departmentName = segregatedEvidences[labId]?.departments[departmentId]?.name || 'Unknown Department';
    const departmentPriority = segregatedEvidences[labId]?.departments[departmentId]?.priority;

    return (
      <TouchableOpacity
        style={[
          styles.evidenceCard,
          isSelected && styles.selectedEvidenceCard
        ]}
        onPress={() => toggleEvidenceSelection(labId, departmentId, evidence)}
      >
        <View style={styles.evidenceHeader}>
          <Text style={styles.evidenceTitle} numberOfLines={1}>
            {evidence.title || `Evidence ID: ${evidence._id.substring(evidence._id.length - 6)}`}
          </Text>
          <View style={styles.evidenceTypeContainer}>
            <Text style={styles.evidenceType}>{evidence.type || "Unknown Type"}</Text>
            <View style={styles.departmentContainer}>
              <Text style={styles.departmentLabel}>Department: </Text>
              <Text style={styles.departmentName}>{departmentName}</Text>
              {departmentPriority && (
                <View style={styles.priorityContainer}>
                  <Text style={styles.priorityLabel}> • Priority: </Text>
                  <Text style={styles.priorityValue}>{departmentPriority}</Text>
                </View>
              )}
            </View>
          </View>
        </View>
        
        {hasAttachments ? (
          <FlatList
            horizontal
            data={evidence.attachmentUrl}
            keyExtractor={(item, index) => `attachment-${index}`}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={styles.mediaItem}
                onPress={() => openMediaModal(item)}
              >
                {isMediaVideo(item) ? (
                  <View style={styles.videoContainer}>
                    <Image
                      source={{ uri: transformUrl(item.replace(/\.(mp4|mov|avi)$/, '.jpg')) }}
                      style={styles.attachmentImage}
                      resizeMode="cover"
                    />
                    <View style={styles.videoOverlay}>
                      <MaterialIcons name="play-circle-fill" size={40} color="white" />
                      <Text style={styles.videoLabel}>VIDEO</Text>
                    </View>
                  </View>
                ) : (
                  <Image
                    source={{ uri: transformUrl(item) }}
                    style={styles.attachmentImage}
                    resizeMode="cover"
                  />
                )}
                <View style={styles.attachmentNumberContainer}>
                  <Text style={styles.attachmentNumber}>{index + 1}</Text>
                </View>
              </TouchableOpacity>
            )}
          />
        ) : (
          <View style={styles.noAttachmentsContainer}>
            <MaterialIcons name="image-not-supported" size={40} color="#ccc" />
            <Text style={styles.noAttachmentsText}>No Media Available</Text>
          </View>
        )}
        
        {evidence.description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionLabel}>Description: <Text style={styles.descriptionText} numberOfLines={2}>{evidence.description} </Text></Text>
     
          </View>
        )}

        {/* Only show lab name */}
        {/* {evidence.labId && (
          <Text style={styles.evidenceLabel}>
            Lab: <Text style={styles.evidenceType}>
              {typeof evidence.labId === 'object' ? evidence.labId.name : 'Unknown Lab'}
            </Text>
          </Text>
        )} */}
        
        {isSelected && (
          <View style={styles.checkmarkOverlay}>
            <MaterialIcons name="check-circle" size={24} color={Colors.primary} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderDepartmentSection = (departmentId, department, labId) => {
    const departmentName = department.name || 'Unknown Department';
    const evidences = department.evidences || [];
    const departmentPriority = department.priority;

    return (
      <View key={departmentId} style={styles.departmentSection}>
        <View style={styles.departmentHeader}>
          <View style={styles.departmentTitleContainer}>
            <Text style={styles.departmentName}>{departmentName}</Text>
            {departmentPriority && (
              <View style={styles.departmentPriorityContainer}>
                <Text style={styles.departmentPriorityLabel}>Priority: </Text>
                <Text style={styles.departmentPriorityValue}>{departmentPriority}</Text>
              </View>
            )}
          </View>
          <Text style={styles.evidenceCount}>({evidences.length} {evidences.length === 1 ? 'evidence' : 'evidences'})</Text>
        </View>

        <View style={styles.evidenceGrid}>
          {evidences.map(evidence => (
            <View key={evidence._id} style={styles.evidenceContainer}>
              {renderEvidenceCard(evidence, labId, departmentId)}
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Loading state
  if (loading) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0a34a1" />
          <Text style={styles.loadingText}>Loading evidence data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error state
  if (error) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.container}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // No evidence data
  if (!segregatedEvidences || Object.keys(segregatedEvidences).length === 0) {
    return (
      <SafeAreaView style={styles.safeAreaContainer}>
        <StatusBar style="dark" />
        <View style={styles.container}>
          <Text style={styles.errorText}>No evidence data available.</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      {/* Preview Component should be one of the first elements */}
      {modalVisible && (
        <View style={styles.previewOverlay}>
          <PreviewComponent
            uri={selectedMedia}
            onClose={() => setModalVisible(false)}
          />
        </View>
      )}

      <StatusBar style="dark" />
      
      <View style={styles.headerContainer}>
        <View style={styles.titleContainer}>
          <Text style={styles.headerTitle}>Evidence for Dispatch</Text>
          <Text style={styles.subTitle}>
            {data?.caseId?.title || 'Case Details'}
          </Text>
        </View>
        
        <View style={styles.selectionControls}>
          <TouchableOpacity
            style={styles.selectAllButton}
            onPress={toggleAllEvidences}
          >
            <MaterialIcons
              name={allEvidencesSelected ? "check-box" : "check-box-outline-blank"}
              size={22}
              color="#0a34a1"
            />
            <Text style={styles.selectAllText}>
              {allEvidencesSelected ? "Deselect All" : "Select All"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.container}>
        {Object.entries(segregatedEvidences).map(([labId, lab]) => {
          const labName = 'Central Forensic Science Laboratory ';
          let totalEvidenceCount = 0;
          
          Object.values(lab.departments).forEach(dept => {
            totalEvidenceCount += dept.evidences.length;
          });
          
          return (
            <View key={labId} style={styles.labContainer}>
              <View style={styles.labHeader}>
                <Text style={styles.labName}>{labName}</Text>
                <View style={styles.labCountContainer}>
                  <Text style={styles.labCountText}>
                    {totalEvidenceCount} {totalEvidenceCount === 1 ? 'Evidence' : 'Evidences'}
                  </Text>
                </View>
              </View>
              
              {Object.entries(lab.departments).map(([departmentId, department]) => 
                renderDepartmentSection(departmentId, department, labId)
              )}
            </View>
          );
        })}
      </ScrollView>
      
      {/* Dispatch button at the bottom */}
      <View style={styles.bottomBar}>
        <View style={styles.selectionSummary}>
          <Text style={styles.selectionCount}>
            {Object.keys(selectedEvidences).length} of {countTotalEvidences()} selected
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.dispatchButton,
            Object.keys(selectedEvidences).length === 0 && styles.disabledButton,
            isSubmitting && styles.submittingButton,
          ]}
          onPress={handleDispatch}
          disabled={Object.keys(selectedEvidences).length === 0 || isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <MaterialIcons name="send" size={18} color="#fff" style={styles.dispatchIcon} />
              <Text style={styles.dispatchButtonText}>Dispatch Selected</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      {showSuccessScreen && (
        <SuccessScreen 
          message="Evidence dispatched successfully to the labs!" 
          duration={3000}
          onComplete={() => {}}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerContainer: {
    backgroundColor: '#fff',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    fontFamily: 'Roboto_bold',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0a34a1',
    fontFamily: 'Roboto_bold',
  },
  subTitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  selectionControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  selectAllText: {
    marginLeft: 4,
    color: '#0a34a1',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
    fontFamily: 'Roboto',
  },
  labContainer: {
    marginHorizontal: 12,
    marginVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  labHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  labName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    fontFamily: 'Roboto_bold',
  },
  labCountContainer: {
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  labCountText: {
    fontSize: 13,
    color: '#0a34a1',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  departmentSection: {
    marginBottom: 16,
    fontFamily: 'Roboto',
  },
  departmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    fontFamily: 'Roboto_bold',
  },
  departmentTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  departmentName: {
    fontSize: 15,
    fontWeight: '600',
    color: '#444',
    marginRight: 8,
    fontFamily: 'Roboto',
  },
  departmentPriorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f4fd',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  departmentPriorityLabel: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: '500',
  },
  departmentPriorityValue: {
    fontSize: 12,
    color: '#0a34a1',
    fontWeight: 'bold',
  },
  evidenceCount: {
    fontSize: 13,
    color: '#666',
    fontFamily: 'Roboto',
  },
  evidenceGrid: {
    marginLeft: 4,
  },
  evidenceContainer: {
    marginBottom: 12,
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginBottom: 12,
    padding: 12,
    borderRadius: 10,
  },
  selectedEvidenceCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
    backgroundColor: `${Colors.primary}10`,
  },
  evidenceHeader: {
    marginBottom: 10,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    fontWeight: '500',
  },
  evidenceTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  evidenceType: {
    fontSize: 13,
    color: Colors.lightText,
  },
  departmentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  departmentLabel: {
    fontSize: 13,
    color: Colors.lightText,
    fontWeight: '500',
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityLabel: {
    fontSize: 13,
    color: Colors.lightText,
    fontWeight: '500',
  },
  priorityValue: {
    fontSize: 13,
    color: '#0a34a1',
    fontWeight: 'bold',
    backgroundColor: '#f0f4ff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  mediaItem: {
    width: 120,
    height: 120,
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  attachmentImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  attachmentNumberContainer: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  attachmentNumber: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: Colors.background,
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    marginTop: 8,
  },
  noAttachmentsContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 10,
  },
  noAttachmentsText: {
    color: '#999',
    fontSize: 14,
    marginTop: 8,
  },
  descriptionContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    fontFamily: 'Roboto',
  },
  descriptionLabel: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666',
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  descriptionText: {
    fontSize: 13,
    color: '#555',
    fontFamily: 'Roboto',
  },
  checkmarkOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  bottomBar: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  selectionSummary: {
    flex: 1,
  },
  selectionCount: {
    fontSize: 14,
    color: '#666',
  },
  dispatchButton: {
    backgroundColor: '#0a34a1',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    fontFamily: 'Roboto_bold',
  },
  disabledButton: {
    backgroundColor: '#9eb0d5',
  },
  submittingButton: {
    backgroundColor: '#0a34a1',
    fontFamily: 'Roboto_bold',
    opacity: 0.7,
  },
  dispatchIcon: {
    marginRight: 8,
  },
  dispatchButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    marginTop: 20,
    padding: 16,
  },
  backButton: {
    backgroundColor: '#0a34a1',
    padding: 10,
    borderRadius: 8,
    width: 100,
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 20,

  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  evidenceLabel: {
    fontSize: 13,
    color: Colors.lightText,
    marginTop: 8,
  },
  previewOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure it's above other content
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
});

export default ForensicRequestDetails;